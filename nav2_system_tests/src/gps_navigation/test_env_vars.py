#!/usr/bin/env python3

import os

def test_env_vars():
    print("=== 测试环境变量保存和恢复 ===")
    
    # 显示当前环境变量
    print("当前环境变量:")
    print(f"  ROS_DOMAIN_ID: {os.environ.get('ROS_DOMAIN_ID', 'NOT SET')}")
    print(f"  RMW_IMPLEMENTATION: {os.environ.get('RMW_IMPLEMENTATION', 'NOT SET')}")
    
    # 保存环境变量
    saved_domain_id = os.environ.get('ROS_DOMAIN_ID')
    saved_rmw = os.environ.get('RMW_IMPLEMENTATION')
    
    print(f"\n保存的环境变量:")
    print(f"  saved_domain_id: {saved_domain_id}")
    print(f"  saved_rmw: {saved_rmw}")
    
    # 模拟恢复逻辑
    print(f"\n恢复逻辑测试:")
    
    # ROS_DOMAIN_ID 恢复逻辑
    if saved_domain_id:
        print(f"恢复 ROS_DOMAIN_ID={saved_domain_id}")
    elif 'ROS_DOMAIN_ID' in os.environ:
        print(f"保持当前 ROS_DOMAIN_ID={os.environ.get('ROS_DOMAIN_ID')}")
    else:
        print("ROS_DOMAIN_ID 未设置")
    
    # RMW_IMPLEMENTATION 恢复逻辑
    if saved_rmw:
        print(f"恢复 RMW_IMPLEMENTATION={saved_rmw}")
    elif 'RMW_IMPLEMENTATION' in os.environ:
        print(f"保持当前 RMW_IMPLEMENTATION={os.environ.get('RMW_IMPLEMENTATION')}")
    else:
        print("RMW_IMPLEMENTATION 未设置")

if __name__ == '__main__':
    test_env_vars()
